package com.ruoyi.yanbao.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.yanbao.entity.CarBrand;
import com.ruoyi.yanbao.mapper.CarBrandMapper;
import com.ruoyi.yanbao.service.CarBrandService;

/**
 * 品牌管理 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Service
public class CarBrandServiceImpl extends ServiceImpl<CarBrandMapper, CarBrand> implements CarBrandService {

    @Autowired
    private CarBrandMapper carBrandMapper;

    /**
     * 查询所有品牌
     *
     * @return 品牌列表
     */
    @Override
    public List<CarBrand> selectCarBrandAll()
    {
        QueryWrapper<CarBrand> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("name");
        return carBrandMapper.selectList(queryWrapper);
    }
}
