<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item label="车系名称" prop="name">
            <el-input
               v-model="queryParams.name"
               placeholder="请输入车系名称"
               clearable
               style="width: 200px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>

         <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="车系状态" clearable style="width: 200px">
               <el-option
                  v-for="option in statusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
               v-hasPermi="['system:carSeries:add']"
            >新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="success"
               plain
               icon="Edit"
               :disabled="single"
               @click="handleUpdate"
               v-hasPermi="['system:carSeries:edit']"
            >修改</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="danger"
               plain
               icon="Delete"
               :disabled="multiple"
               @click="handleDelete"
               v-hasPermi="['system:carSeries:remove']"
            >删除</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="carSeriesList" @selection-change="handleSelectionChange" @sort-change="handleSortChange" stripe>
         <el-table-column type="selection" width="60" align="center" />
         <el-table-column label="车系名称" align="center" prop="name" :show-overflow-tooltip="true" sortable="custom" />
         <el-table-column label="品牌" align="center" prop="brandName" :show-overflow-tooltip="true"/>
         <el-table-column label="排序" align="center" prop="sort" sortable="custom">
            <template #default="scope">
               <el-input-number
                  v-if="scope.row.editingSort"
                  v-model="scope.row.sort"
                  :min="0"
                  :max="9999"
                  size="small"
                  controls-position="right"
                  @blur="handleSortSave(scope.row)"
                  @keyup.enter="handleSortSave(scope.row)"
                  @keyup.esc="handleSortCancel(scope.row)"
                  style="width: 90px;"
                  :data-row-id="scope.row.id"
               />
               <span
                  v-else
                  @click="handleSortEdit(scope.row)"
                  style="cursor: pointer; padding: 6px 12px; border-radius: 4px; background-color: #f0f9ff; border: 1px dashed #409eff; display: inline-block; min-width: 40px; text-align: center;"
                  title="点击编辑排序"
                  v-hasPermi="['system:carSeries:edit']"
               >
                  {{ scope.row.sort }}
               </span>
            </template>
         </el-table-column>
         <el-table-column label="状态" align="center" prop="status" width="100">
            <template #default="scope">
               <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
                  {{ scope.row.status === 1 ? '正常' : '停用' }}
               </el-tag>
            </template>
         </el-table-column>
         <el-table-column label="操作" width="260" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:carSeries:edit']" size="small">修改</el-button>
               <el-divider direction="vertical" />
               <el-button
                  link
                  :type="scope.row.status === 1 ? 'warning' : 'success'"
                  @click="handleStatusChange(scope.row)"
                  v-hasPermi="['system:carSeries:edit']"
                  size="small"
               >
                  {{ scope.row.status === 1 ? '停用' : '启用' }}
               </el-button>
               <el-divider direction="vertical" />
               <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:carSeries:remove']" size="small">删除</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 添加或修改车系管理对话框 -->
      <el-dialog :title="title" v-model="open" width="500px" append-to-body>
         <el-form ref="carSeriesRef" :model="form" :rules="rules" label-width="100px">
            <el-form-item label="车系名称" prop="name">
               <el-input v-model="form.name" placeholder="请输入车系名称" />
            </el-form-item>
            <el-form-item label="品牌" prop="carBrandId">
               <el-select v-model="form.carBrandId" placeholder="请选择品牌" style="width: 100%">
                  <el-option
                     v-for="brand in brandOptions"
                     :key="brand.id"
                     :label="brand.name"
                     :value="brand.id"
                  />
               </el-select>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
               <el-input-number v-model="form.sort" controls-position="right" :min="0" style="width: 100%" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
               <el-radio-group v-model="form.status">
                  <el-radio
                     v-for="option in statusOptions"
                     :key="option.value"
                     :value="option.value"
                  >{{ option.label }}</el-radio>
               </el-radio-group>
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="CarSeries">
import { listCarSeries, addCarSeries, delCarSeries, getCarSeries, updateCarSeries, changeCarSeriesStatus } from "@/api/system/carSeries"
import { optionSelectCarBrand } from "@/api/system/carBrand"

const { proxy } = getCurrentInstance()

const carSeriesList = ref([])
const brandOptions = ref([])
const statusOptions = ref([
  { label: '正常', value: 1 },
  { label: '停用', value: 0 }
])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    status: undefined,
    orderByColumn: undefined,
    isAsc: undefined
  },
  rules: {
    name: [{ required: true, message: "车系名称不能为空", trigger: "blur" }],
    carBrandId: [{ required: true, message: "品牌ID不能为空", trigger: "blur" }],
    sort: [{ required: true, message: "排序不能为空", trigger: "blur" }],
    status: [{ required: true, message: "状态不能为空", trigger: "change" }]
  }
})

const { queryParams, form, rules } = toRefs(data)



/** 查询车系管理列表 */
function getList() {
  loading.value = true
  listCarSeries(queryParams.value).then(response => {
    carSeriesList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 查询品牌下拉选项 */
function getBrandOptions() {
  optionSelectCarBrand().then(response => {
    brandOptions.value = response.data
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    name: undefined,
    carBrandId: 1, // 默认捷途品牌
    sort: 0,
    status: 1
  }
  proxy.resetForm("carSeriesRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 排序触发事件 */
function handleSortChange(column) {
  queryParams.value.orderByColumn = column.prop
  queryParams.value.isAsc = column.order === 'ascending' ? 'asc' : 'desc'
  getList()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  getBrandOptions()
  open.value = true
  title.value = "添加车系"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  getBrandOptions()
  const id = row.id || ids.value
  getCarSeries(id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改车系"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["carSeriesRef"].validate(valid => {
    if (valid) {
      if (form.value.id != undefined) {
        updateCarSeries(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addCarSeries(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const carSeriesIds = row.id || ids.value
  let deleteNames = ''

  if (row.id) {
    // 单个删除
    deleteNames = row.name
  } else {
    // 批量删除
    const selectedRows = carSeriesList.value.filter(item => ids.value.includes(item.id))
    deleteNames = selectedRows.map(item => item.name).join('、')
  }

  proxy.$modal.confirm('是否确认删除车系"' + deleteNames + '"？').then(function() {
    return delCarSeries(carSeriesIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 状态修改 */
function handleStatusChange(row) {
  let newStatus = row.status === 1 ? 0 : 1
  let text = newStatus === 1 ? "启用" : "停用"
  proxy.$modal.confirm('确认要"' + text + '""' + row.name + '"车系吗？').then(function() {
    return changeCarSeriesStatus(row.id, newStatus)
  }).then(() => {
    row.status = newStatus
    proxy.$modal.msgSuccess(text + "成功")
  }).catch(function() {
    // 操作失败时不需要恢复状态，因为我们没有预先改变状态
  })
}

/** 排序编辑 */
function handleSortEdit(row) {
  // 保存原始值，用于取消编辑时恢复
  row.originalSort = row.sort
  // 设置编辑状态
  row.editingSort = true
  // 下一帧聚焦到输入框
  nextTick(() => {
    const inputElement = document.querySelector(`[data-row-id="${row.id}"] .el-input-number input`)
    if (inputElement) {
      inputElement.focus()
      inputElement.select()
    }
  })
}

/** 排序保存 */
function handleSortSave(row) {
  // 验证排序值
  if (row.sort === null || row.sort === undefined || row.sort < 0) {
    proxy.$modal.msgError("排序值不能为空且不能小于0")
    row.sort = row.originalSort
    row.editingSort = false
    return
  }

  // 如果值没有变化，直接退出编辑状态
  if (row.sort === row.originalSort) {
    row.editingSort = false
    return
  }

  // 调用更新接口
  const updateData = {
    id: row.id,
    sort: row.sort
  }

  updateCarSeries(updateData).then(() => {
    proxy.$modal.msgSuccess("排序更新成功")
    row.editingSort = false
    delete row.originalSort
    // 刷新列表以确保排序正确
    getList()
  }).catch(() => {
    proxy.$modal.msgError("排序更新失败")
    // 恢复原始值
    row.sort = row.originalSort
    row.editingSort = false
    delete row.originalSort
  })
}

/** 取消排序编辑 */
function handleSortCancel(row) {
  // 恢复原始值
  row.sort = row.originalSort
  row.editingSort = false
  delete row.originalSort
}
getList()
getBrandOptions()
</script>
