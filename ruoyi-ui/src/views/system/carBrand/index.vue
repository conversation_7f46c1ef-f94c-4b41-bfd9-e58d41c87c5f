<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item label="品牌名称" prop="name">
            <el-input
               v-model="queryParams.name"
               placeholder="请输入品牌名称"
               clearable
               style="width: 200px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>

         <el-form-item label="首字母" prop="firstLetter">
            <el-input
               v-model="queryParams.firstLetter"
               placeholder="请输入首字母"
               clearable
               style="width: 200px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>

         <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="品牌状态" clearable style="width: 200px">
               <el-option
                  v-for="option in statusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
               v-hasPermi="['system:carBrand:add']"
            >新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="success"
               plain
               icon="Edit"
               :disabled="single"
               @click="handleUpdate"
               v-hasPermi="['system:carBrand:edit']"
            >修改</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="danger"
               plain
               icon="Delete"
               :disabled="multiple"
               @click="handleDelete"
               v-hasPermi="['system:carBrand:remove']"
            >删除</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="carBrandList" @selection-change="handleSelectionChange" @sort-change="handleSortChange" stripe>
         <el-table-column type="selection" width="60" align="center" />
         <el-table-column label="品牌名称" align="center" prop="name" :show-overflow-tooltip="true" sortable="custom" />
         <el-table-column label="首字母" align="center" prop="firstLetter" width="100" />
         <el-table-column label="排序" align="center" prop="sort" sortable="custom">
            <template #default="scope">
               <el-input-number
                  v-if="scope.row.editingSort"
                  v-model="scope.row.sort"
                  :min="0"
                  :max="9999"
                  size="small"
                  controls-position="right"
                  @blur="handleSortSave(scope.row)"
                  @keyup.enter="handleSortSave(scope.row)"
                  @keyup.esc="handleSortCancel(scope.row)"
                  style="width: 90px;"
                  :data-row-id="scope.row.id"
               />
               <span
                  v-else
                  @click="handleSortEdit(scope.row)"
                  style="cursor: pointer; padding: 6px 12px; border-radius: 4px; background-color: #f0f9ff; border: 1px dashed #409eff; display: inline-block; min-width: 40px; text-align: center;"
                  title="点击编辑排序"
                  v-hasPermi="['system:carBrand:edit']"
               >
                  {{ scope.row.sort }}
               </span>
            </template>
         </el-table-column>
         <el-table-column label="汽车之家品牌ID" align="center" prop="autohomeBrandId" width="150" />
         <el-table-column label="状态" align="center" prop="status" width="100">
            <template #default="scope">
               <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
                  {{ scope.row.status === 1 ? '正常' : '停用' }}
               </el-tag>
            </template>
         </el-table-column>
         <el-table-column label="操作" width="260" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:carBrand:edit']" size="small">修改</el-button>
               <el-divider direction="vertical" />
               <el-button
                  link
                  :type="scope.row.status === 1 ? 'danger' : 'success'"
                  icon="Switch"
                  @click="handleStatusChange(scope.row)"
                  v-hasPermi="['system:carBrand:edit']"
                  size="small"
               >
                  {{ scope.row.status === 1 ? '停用' : '启用' }}
               </el-button>
               <el-divider direction="vertical" />
               <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:carBrand:remove']" size="small">删除</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 添加或修改品牌管理对话框 -->
      <el-dialog :title="title" v-model="open" width="500px" append-to-body>
         <el-form ref="carBrandRef" :model="form" :rules="rules" label-width="120px">
            <el-form-item label="品牌名称" prop="name">
               <el-input v-model="form.name" placeholder="请输入品牌名称" />
            </el-form-item>
            <el-form-item label="首字母" prop="firstLetter">
               <el-input v-model="form.firstLetter" placeholder="请输入首字母" maxlength="1" />
            </el-form-item>
            <el-form-item label="排序" prop="sort">
               <el-input-number v-model="form.sort" controls-position="right" :min="0" style="width: 100%" />
            </el-form-item>
            <el-form-item label="汽车之家品牌ID" prop="autohomeBrandId">
               <el-input v-model="form.autohomeBrandId" placeholder="请输入汽车之家对应的品牌ID" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
               <el-radio-group v-model="form.status">
                  <el-radio
                     v-for="option in statusOptions"
                     :key="option.value"
                     :value="option.value"
                  >{{ option.label }}</el-radio>
               </el-radio-group>
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="CarBrand">
import { listCarBrand, addCarBrand, delCarBrand, getCarBrand, updateCarBrand, changeCarBrandStatus } from "@/api/system/carBrand"

const { proxy } = getCurrentInstance()

const carBrandList = ref([])
const statusOptions = ref([
  { label: '正常', value: 1 },
  { label: '停用', value: 0 }
])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    firstLetter: undefined,
    status: undefined,
    orderByColumn: undefined,
    isAsc: undefined
  },
  rules: {
    name: [{ required: true, message: "品牌名称不能为空", trigger: "blur" }],
    firstLetter: [{ required: true, message: "首字母不能为空", trigger: "blur" }],
    sort: [{ required: true, message: "排序不能为空", trigger: "blur" }],
    status: [{ required: true, message: "状态不能为空", trigger: "change" }]
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询品牌管理列表 */
function getList() {
  loading.value = true
  listCarBrand(queryParams.value).then(response => {
    carBrandList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    name: undefined,
    firstLetter: undefined,
    sort: 0,
    status: 1,
    autohomeBrandId: undefined
  }
  proxy.resetForm("carBrandRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 排序触发事件 */
function handleSortChange(column) {
  queryParams.value.orderByColumn = column.prop
  queryParams.value.isAsc = column.order === 'ascending' ? 'asc' : 'desc'
  getList()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加品牌"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value
  getCarBrand(id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改品牌"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["carBrandRef"].validate(valid => {
    if (valid) {
      if (form.value.id != undefined) {
        updateCarBrand(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addCarBrand(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除品牌编号为"' + _ids + '"的数据项？').then(function() {
    return delCarBrand(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 状态修改 */
function handleStatusChange(row) {
  let text = row.status === 1 ? "停用" : "启用"
  proxy.$modal.confirm('确认要"' + text + '""' + row.name + '"品牌吗？').then(function() {
    return changeCarBrandStatus(row.id, row.status === 1 ? 0 : 1)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess(text + "成功")
  }).catch(function() {
    row.status = row.status === 0 ? 1 : 0
  })
}

/** 排序编辑 */
function handleSortEdit(row) {
  row.originalSort = row.sort
  row.editingSort = true
  nextTick(() => {
    const input = document.querySelector(`[data-row-id="${row.id}"] input`)
    if (input) {
      input.focus()
      input.select()
    }
  })
}

/** 排序保存 */
function handleSortSave(row) {
  if (row.sort !== row.originalSort) {
    updateCarBrand(row).then(response => {
      proxy.$modal.msgSuccess("排序修改成功")
      row.editingSort = false
      delete row.originalSort
      getList()
    }).catch(() => {
      row.sort = row.originalSort
      row.editingSort = false
      delete row.originalSort
    })
  } else {
    row.editingSort = false
    delete row.originalSort
  }
}

/** 排序取消 */
function handleSortCancel(row) {
  row.sort = row.originalSort
  row.editingSort = false
  delete row.originalSort
}

getList()
</script>
