package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.yanbao.entity.CarSeries;
import com.ruoyi.yanbao.service.CarBrandService;
import com.ruoyi.yanbao.service.CarSeriesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 车系管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@RestController
@RequestMapping("/system/carSeries")
public class CarSeriesController extends BaseController
{
    @Autowired
    private CarSeriesService carSeriesService;

    @Autowired
    private CarBrandService carBrandService;

    /**
     * 查询车系管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:carSeries:list')")
    @GetMapping("/list")
    public TableDataInfo list(CarSeries carSeries)
    {
        startPage();
        List<CarSeries> list = carSeriesService.selectCarSeriesListWithBrand(carSeries);
        return getDataTable(list);
    }

    /**
     * 获取车系管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:carSeries:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(carSeriesService.getById(id));
    }

    /**
     * 新增车系管理
     */
    @PreAuthorize("@ss.hasPermi('system:carSeries:add')")
    @Log(title = "车系管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody CarSeries carSeries)
    {
        if (!carSeriesService.checkCarSeriesNameUnique(carSeries))
        {
            return error("新增车系'" + carSeries.getName() + "'失败，车系名称已存在");
        }
        carSeries.setCreatedBy(getUsername());
        return toAjax(carSeriesService.save(carSeries));
    }

    /**
     * 修改车系管理
     */
    @PreAuthorize("@ss.hasPermi('system:carSeries:edit')")
    @Log(title = "车系管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody CarSeries carSeries)
    {
        if (!carSeriesService.checkCarSeriesNameUnique(carSeries))
        {
            return error("修改车系'" + carSeries.getName() + "'失败，车系名称已存在");
        }
        carSeries.setChangedBy(getUsername());
        return toAjax(carSeriesService.updateById(carSeries));
    }

    /**
     * 删除车系管理
     */
    @PreAuthorize("@ss.hasPermi('system:carSeries:remove')")
    @Log(title = "车系管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(carSeriesService.deleteCarSeriesByIds(ids));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:carSeries:edit')")
    @Log(title = "车系管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody CarSeries carSeries)
    {
        carSeries.setChangedBy(getUsername());
        
        return toAjax(carSeriesService.updateById(carSeries));
    }

    /**
     * 获取车系选择框列表
     */
    @GetMapping("/optionSelect")
    public AjaxResult optionSelect()
    {
        List<CarSeries> carSeries = carSeriesService.selectCarSeriesAll();
        return success(carSeries);
    }

    /**
     * 获取品牌选择框列表
     */
    @GetMapping("/brandOptions")
    public AjaxResult brandOptions()
    {
        return success(carBrandService.selectCarBrandAll());
    }

    /**
     * 排序操作
     */
    @PreAuthorize("@ss.hasPermi('system:carSeries:edit')")
    @Log(title = "车系管理", businessType = BusinessType.UPDATE)
    @PutMapping("/sort")
    public AjaxResult updateSort(@RequestBody List<CarSeries> carSeriesList)
    {
        return toAjax(carSeriesService.updateCarSeriesSort(carSeriesList));
    }
}
